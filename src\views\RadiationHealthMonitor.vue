<template>
    <div class="index-container">
        <TopNav />
        <div class="main-content">
            <SideNav @nav-click="handleNavClick" />
            <div class="content">
                <ContentTabs ref="contentTabsRef" />
                <div class="content-container">
                    <img class="contentImg" src="../assets/img/放射卫生在线监测.png" alt="" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import TopNav from '../components/TopNav.vue';
import SideNav from '@/components/SideNav.vue';
import ContentTabs from '@/components/ContentTabs.vue';

// 组件引用
const contentTabsRef = ref(null)

// 处理侧边栏导航点击
const handleNavClick = ({ index }) => {
    // 切换ContentTabs中的机构信息
    if (contentTabsRef.value) {
        contentTabsRef.value.switchInstitution(index)
    }
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.index-container {
    width: 100%;
    height: 100%;
    background: url(../assets/img/background.png);
    background-size: cover;
    background-repeat: no-repeat;
}

.main-content {
    display: flex;
    height: calc(100vh - 60px);
}

.content {
    width: calc(100vw - 350px);
    height: calc(100vh - 60px);
    overflow: hidden;
}

.content-container {
    width: 100%;
    overflow: hidden;
    background-color: rgba(12, 25, 44, 0.86);
    padding: 30px 40px;
}

.contentImg {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain;
}
</style>