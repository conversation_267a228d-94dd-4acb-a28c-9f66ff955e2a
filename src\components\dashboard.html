<!DOCTYPE html>
<html>
    <head>
    <meta charset="utf-8" />
    <title>www.makeApie.cn - ECharts案例下载 - 综合评估仪表盘</title>
    <base href="https://www.makeapie.cn/">
    
    <!-- 引入刚刚下载的 ECharts 文件 和 jquery -->
    <script type="text/javascript" src="https://registry.npmmirror.com/echarts/4.2.1/files/dist/echarts.min.js"></script>
    <script type="text/javascript" src="https://registry.npmmirror.com/jquery/3.7.1/files/dist/jquery.min.js"></script>
    <style>*{padding: 0; margin: 0;}</style>
    
    <!--
        🚨🚨非常重要🚨🚨：下面是运行案例需要的第三方脚本，如果是相对链接，下载方法看上面👆👆
        如果你需要本地使用，请下载下来，假设下面代码中有个相对路径资源 /dep/echarts/map/js/china.js，下载方法：https://www.makeapie.cn/dep/echarts/map/js/china.js
    -->
    <!--暂无第三方脚本-->

    </head>
    <body>
    <!-- 为 ECharts 准备一个定义了宽高的 DOM -->
    <div id="chart-panel" style="width: calc(100vw - 200px);height:calc(100vh - 200px); padding: 100px; margin: 0 auto;"></div>
    <script type="text/javascript">
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('chart-panel'));
        // 指定图表的配置项和数据
        var option = {};

        //=============== start ===============//
        /**
         * 🚨🚨非常重要🚨🚨
         * 下面是 网站左侧代码，核心代码
         */
        option = {
    backgroundColor: '#fff',

    series: [{
            type: 'gauge',
            radius: '60%',
            startAngle: '215',
            endAngle: '-35',
            splitNumber: 50,
            detail: {
                offsetCenter: [0, -20],
                formatter: ' '
            },
            pointer: {
                show: false
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: [
                        [0, '#7691FA'],
                        [52 / 100, `red`],
                        [1, '#e9e9e9']
                    ],
                    width: 45
                }
            },
            axisTick: {
                show: false
            },
            splitLine: {
                show: true,
                length: 45,
                lineStyle: {
                    color: '#fff',
                    width: 6
                }
            },
            axisLabel: {
                show: false
            }
        },
        {
            type: 'gauge',
            radius: '48%',
            startAngle: '212',
            endAngle: '-32',
            splitNumber: 45,
            pointer: {
                show: false
            },
            detail: {
                offsetCenter: [0, -5],
                formatter: `{a|综合评估}\n{b|${
                            52
                        }}\n{x|较差}\n`,
                rich: {
                    a: {
                        color: '#404346',
                        lineHeight: 35,
                        fontSize: 22,
                        fontWeight: 550
                    },
                    b: {
                        color: 'red',
                        fontSize: 32,
                        fontWeight: 550,
                        padding: [10, 0, 10, 0]
                    },
                    x: {
                        fontSize: 18,
                        color: 'red'
                    },
                    f: {
                        fontSize: 14,
                        color: '#404346',
                        width: 80
                    }
                }
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: [
                        [0, '#e9e9e9'],
                        [1, '#e9e9e9']
                    ],
                    width: 8
                }
            },
            axisTick: {
                show: false
            },
            splitLine: {
                show: true,
                length: 8,
                lineStyle: {
                    color: '#fff',
                    width: 5
                }
            },
            axisLabel: {
                show: false
            }
        }
    ]
};
        //=============== end ===============//
        
        // 使用刚指定的配置项和数据显示图表。
        if (option && typeof option === 'object') {
        myChart.setOption(option);
        }
        window.addEventListener('resize', myChart.resize);
    </script>
    </body>
</html>