import { createRouter, createWebHashHistory } from 'vue-router'
import IndexView from '../views/IndexView.vue'
import PublicPlaceMonitor from '../views/PublicPlaceMonitor.vue'
import RadiationHealthMonitor from '../views/RadiationHealthMonitor.vue'
import OccupationalHazardMonitor from '../views/OccupationalHazardMonitor.vue'
import RealTimeVideoMonitor from '../views/RealTimeVideoMonitor.vue'
import SupportAppManagement from '../views/SupportAppManagement.vue'
import ComponentDemo from '../views/ComponentDemo.vue'

const routes = [
  {
    path: '/',
    name: 'Index',
    component: IndexView
  },
  {
    path: '/public-place-monitor',
    name: 'PublicPlaceMonitor',
    component: PublicPlaceMonitor
  },
  {
    path: '/radiation-health-monitor',
    name: 'RadiationHealthMonitor',
    component: RadiationHealthMonitor
  },
  {
    path: '/occupational-hazard-monitor',
    name: 'OccupationalHazardMonitor',
    component: OccupationalHazardMonitor
  },
  {
    path: '/real-time-video-monitor',
    name: 'RealTimeVideoMonitor',
    component: RealTimeVideoMonitor
  },
  {
    path: '/support-app-management',
    name: 'SupportAppManagement',
    component: SupportAppManagement
  },
  {
    path: '/add-device',
    name: 'AddDevice',
    component: () => import('../views/AddDeviceModal.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(), // 若部署环境需要，也可用 createWebHistory，注意处理路由模式
  routes
})

export default router