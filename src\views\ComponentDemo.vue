<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>监测组件演示</h1>
      <p>展示优化后的组件和新开发的监测数据组件</p>
    </div>

    <div class="demo-content">
      <!-- 优化后的折线图组件 -->
      <div class="demo-section">
        <h2>优化后的折线图组件</h2>
        <p>使用Element Plus时间范围选择器替换原有的两个独立时间选择器</p>
        <LineChartComponent 
          :indicator-data="indicatorData" 
          :chart-data="chartData"
          @time-range-change="handleTimeRangeChange" 
          @data-update="handleDataUpdate" 
        />
      </div>

      <!-- 优化后的色带组件 -->
      <div class="demo-section">
        <h2>优化后的色带组件</h2>
        <p>隐藏数字刻度，添加泡泡框显示等级，使用Element Plus日期选择器</p>
        <ColorBandComponent 
          :pm-value="pmData.value" 
          :initial-date="pmData.date"
          @date-change="handlePmDateChange" 
          @value-update="handlePmValueUpdate" 
        />
      </div>

      <!-- 新开发的气体监测组件 -->
      <div class="demo-section">
        <h2>气体监测数据组件</h2>
        <p>使用ECharts环形仪表盘展示CO和CO₂数据</p>
        <GasMonitorComponent 
          :co-value="gasData.co" 
          :co2-value="gasData.co2"
          :initial-date="gasData.date"
          @date-change="handleGasDateChange"
          @data-update="handleGasDataUpdate"
        />
      </div>

      <!-- 新开发的甲醛监测组件 -->
      <div class="demo-section">
        <h2>甲醛监测数据组件</h2>
        <p>使用CSS 3D变换创建金字塔四棱锥可视化效果</p>
        <FormaldehydeMonitorComponent 
          :formaldehyde-value="formaldehydeData.value"
          :initial-date="formaldehydeData.date"
          @date-change="handleFormaldehydeDateChange"
          @value-update="handleFormaldehydeValueUpdate"
        />
      </div>

      <!-- 数据控制面板 -->
      <div class="demo-section">
        <h2>数据控制面板</h2>
        <p>调整数值来查看组件的动态效果</p>
        <div class="control-panel">
          <div class="control-group">
            <label>PM₁₀数值: {{ pmData.value }}</label>
            <input 
              type="range" 
              min="0" 
              max="300" 
              v-model="pmData.value" 
              class="slider"
            />
          </div>
          
          <div class="control-group">
            <label>CO数值: {{ gasData.co }}</label>
            <input 
              type="range" 
              min="0" 
              max="100" 
              v-model="gasData.co" 
              class="slider"
            />
          </div>
          
          <div class="control-group">
            <label>CO₂数值: {{ gasData.co2 }}</label>
            <input 
              type="range" 
              min="0" 
              max="100" 
              v-model="gasData.co2" 
              class="slider"
            />
          </div>
          
          <div class="control-group">
            <label>甲醛数值: {{ formaldehydeData.value }}</label>
            <input 
              type="range" 
              min="0" 
              max="100" 
              v-model="formaldehydeData.value" 
              class="slider"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import LineChartComponent from '@/components/LineChartComponent.vue'
import ColorBandComponent from '@/components/ColorBandComponent.vue'
import GasMonitorComponent from '@/components/GasMonitorComponent.vue'
import FormaldehydeMonitorComponent from '@/components/FormaldehydeMonitorComponent.vue'

// 指标数据
const indicatorData = reactive({
  temperature: 25.6,
  noise: 45.2,
  hcl: 0.8,
  humidity: 65.3,
  tvoc: 0.12
})

// 图表数据
const chartData = reactive({
  times: ['18:00', '18:01', '18:02', '18:03', '18:04', '18:05'],
  values: [25.6, 26.1, 25.8, 26.3, 25.9, 26.0]
})

// PM₁₀数据
const pmData = reactive({
  value: 32,
  date: '2025-07-01'
})

// 气体数据
const gasData = reactive({
  co: 12,
  co2: 36,
  date: '2025-07-01'
})

// 甲醛数据
const formaldehydeData = reactive({
  value: 32,
  date: '2025-07-01'
})

// 事件处理函数
const handleTimeRangeChange = (data) => {
  console.log('时间范围变化:', data)
}

const handleDataUpdate = (data) => {
  console.log('数据更新:', data)
}

const handlePmDateChange = (date) => {
  pmData.date = date
  console.log('PM₁₀日期变化:', date)
}

const handlePmValueUpdate = (value) => {
  console.log('PM₁₀数值更新:', value)
}

const handleGasDateChange = (date) => {
  gasData.date = date
  console.log('气体监测日期变化:', date)
}

const handleGasDataUpdate = (data) => {
  console.log('气体数据更新:', data)
}

const handleFormaldehydeDateChange = (date) => {
  formaldehydeData.date = date
  console.log('甲醛监测日期变化:', date)
}

const handleFormaldehydeValueUpdate = (value) => {
  console.log('甲醛数值更新:', value)
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c192c 0%, #1a2332 100%);
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    color: #ffffff;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  
  p {
    color: #87CEFA;
    font-size: 16px;
  }
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  
  h2 {
    color: #4A90E2;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  
  p {
    color: #87CEFA;
    font-size: 14px;
    margin-bottom: 20px;
  }
}

.control-panel {
  background: rgba(12, 25, 44, 0.9);
  border-radius: 8px;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  label {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
  }
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(74, 144, 226, 0.3);
  outline: none;
  cursor: pointer;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4A90E2;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(74, 144, 226, 0.5);
  }
  
  &::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4A90E2;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(74, 144, 226, 0.5);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .demo-container {
    padding: 10px;
  }
  
  .demo-header {
    h1 {
      font-size: 24px;
    }
    
    p {
      font-size: 14px;
    }
  }
  
  .demo-section {
    h2 {
      font-size: 20px;
    }
  }
  
  .control-panel {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}
</style>
