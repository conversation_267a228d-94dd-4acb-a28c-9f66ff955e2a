<template>
    <div class="index-container">
        <TopNav />
        <div class="main-content">
            <div class="cards-container">
                <div class="function-card" @click="handleClick('public-place')">
                    <img class="icon" src="../assets/img/公共场所监测管理@2x.png" alt="" />
                    <span class="card-title">公共场所在线监测</span>
                </div>
                <div class="function-card" @click="handleClick('radiation-health')">
                    <img class="icon" src="../assets/img/放射卫生@2x.png" alt="" />
                    <span class="card-title">放射卫生在线监测</span>
                </div>
                <div class="function-card" @click="handleClick('occupational-hazard')">
                    <img class="icon" src="../assets/img/职业病防治@2x.png" alt="" />
                    <span class="card-title">职业病危害因素在线监测</span>
                </div>
                <div class="function-card" @click="handleClick('real-time-video')">
                    <img class="icon" src="../assets/img/实时视频监控@2x.png" alt="" />
                    <span class="card-title">实时视频监控</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import TopNav from '../components/TopNav.vue'
const router = useRouter()

const handleClick = (type) => {
    // 根据类型跳转不同路由，这里先写死示例，实际按项目路由配置调整
    switch (type) {
        case 'public-place':
            router.push('/public-place-monitor')
            break
        case 'radiation-health':
            router.push('/radiation-health-monitor')
            break
        case 'occupational-hazard':
            router.push('/occupational-hazard-monitor')
            break
        case 'real-time-video':
            router.push('/real-time-video-monitor')
            break
        default:
            break
    }
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.index-container {
    width: 100%;
    height: 100%;
    background: url(../assets/img/background.png);
    background-size: cover;
    background-repeat: no-repeat;
}

.main-content {
    @include flex-column;
    @include flex-center;
    height: calc(100vh - 60px);
}

.cards-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    width: calc(2 * (584px + 27px * 2));
}

.function-card {
    width: 584px;
    height: 303px;
    margin: 22px 27px;
    background: $bg-primary;
    border-radius: 20px 20px 20px 20px;
    border: 0px solid #040404;
    box-shadow: $shadow-sm;
    @include flex-column;
    @include flex-center;
    cursor: pointer;
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: $shadow-lg;
    }
}

.icon {
    width: 120px;
    height: 120px;
    margin-bottom: 32px;
}


.card-title {
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 45px;
    color: #000000;
    line-height: 60px;
    text-align: center;
    font-style: normal;
    text-transform: none;
}
</style>